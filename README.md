# Ohako Customer Page

## Introduction

Common authentication flow UI following guideline on [Figma](https://www.figma.com/design/2nOOe5akXRoiL4Wm4MNPyu/Web-App-Common-Flow?node-id=7677-33797&t=J1p6fTcJE005JR1x-4). This repository provides UI for login, register, complete profile, forgot password, verify account, server-side route role-based authorization, and JWT token security.

## Requirements

- Node.js 18+
- npm

## Installation

To set up the project, clone the repository and install dependencies using your preferred package manager (npm, yarn, pnpm, or bun).

```bash
npm install
