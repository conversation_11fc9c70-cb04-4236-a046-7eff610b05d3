import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import MockRestaurantService from './restaurant-service';

/**
 * API interceptor that can redirect API calls to mock services
 * This allows seamless switching between mock and real API data
 */
export class ApiInterceptor {
  /**
   * Interceptor for restaurant API calls
   * @param config Axios request configuration
   * @returns Mock response if intercepted, undefined otherwise
   */
  static async interceptRestaurantRequest(
    config: AxiosRequestConfig,
  ): Promise<AxiosResponse | undefined> {
    // Check if mock mode is enabled
    if (!MockRestaurantService.isMockEnabled()) {
      return undefined; // Let the request proceed to real API
    }

    // Check if this is a restaurant API call
    const isRestaurantApi = config.url?.includes('/customer/restaurants');

    if (!isRestaurantApi) {
      return undefined; // Not a restaurant API call, let it proceed
    }

    // Extract query parameters from the request
    const params = config.params || {};

    try {
      // Get mock data from the service
      const mockResponse = await MockRestaurantService.getRestaurants(params);

      // Return mock response in Axios format
      return {
        data: mockResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config,
        request: {},
      } as AxiosResponse;
    } catch (error) {
      // If mock service fails, let the request proceed to real API
      console.error('Mock service error:', error);
      return undefined;
    }
  }

  /**
   * Install the interceptor on an Axios instance
   * @param axiosInstance The Axios instance to install interceptors on
   */
  static install(axiosInstance: any) {
    // Request interceptor
    axiosInstance.interceptors.request.use(
      async (config: AxiosRequestConfig) => {
        // Try to intercept the request
        const mockResponse = await this.interceptRestaurantRequest(config);

        if (mockResponse) {
          // If we have a mock response, we need to prevent the real request
          // and return the mock data. We'll do this by throwing a special error
          // that we can catch in the response interceptor
          const mockError = new Error('Mock response intercepted');
          (mockError as any).__isMockResponse = true;
          (mockError as any).mockResponse = mockResponse;
          (mockError as any).config = config;
          throw mockError;
        }

        return config;
      },
      (error: any) => Promise.reject(error),
    );

    // Response interceptor to handle mock responses
    axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error: any) => {
        // Check if this is our mock response "error"
        if (error.__isMockResponse) {
          return Promise.resolve(error.mockResponse);
        }
        return Promise.reject(error);
      },
    );
  }
}

export default ApiInterceptor;
