/**
 * Mock Data System - Main Exports
 *
 * This file provides clean imports for the mock data system.
 * Use these exports when you need to manually work with mock data.
 */

// Type exports
export type { Restaurant, RestaurantApiResponse, RestaurantQueryParams } from '../models/restaurant/types';

// API interceptor
export { default as ApiInterceptor } from './api-interceptor';

// Mock data
export { mockRestaurants, TOTAL_MOCK_RESTAURANTS } from './restaurant-data';

// Mock service
export { default as MockRestaurantService } from './restaurant-service';
