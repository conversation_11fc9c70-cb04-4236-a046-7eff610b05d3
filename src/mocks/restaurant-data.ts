import type { Restaurant } from 'models/restaurant/types';

// Simple placeholder image generator
const createPlaceholderImage = (text: string) => {
  // Base64 encoded SVG placeholder with restaurant name
  const svg = `<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="400" height="300" fill="#F5F5F5"/>
    <circle cx="200" cy="120" r="40" stroke="#D1D1D1" stroke-width="2" fill="none"/>
    <path d="M180 140L200 120L220 140" stroke="#D1D1D1" stroke-width="2" fill="none"/>
    <text x="200" y="180" text-anchor="middle" fill="#999999" font-family="Arial" font-size="14">${text}</text>
    <text x="200" y="200" text-anchor="middle" fill="#CCCCCC" font-family="Arial" font-size="12">Restaurant Image</text>
  </svg>`;
  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

// Mock restaurant data following the exact API structure
export const mockRestaurants: Restaurant[] = [
  {
    name: '京星',
    nameEn: 'Kyoboshi',
    phoneNumber: '03-3571-1968',
    address: {
      postalCode: '1040061',
      prefectureId: 13,
      prefectureName: '東京都',
      cityId: 13103,
      cityName: '中央区',
      wardId: 1310301,
      wardName: '銀座',
      buildingRoom: '8-7-6 Ginza Building 3F',
    },
    addressEn: '3F Ginza Building, 8-7-6 Ginza, Chuo-ku, Tokyo',
    accessInformation: '地下鉄銀座線「銀座駅」A2出口より徒歩5分',
    accessInformationEn: '5-minute walk from Ginza Station (Exit A2)',
    genre: {
      genreId: 1,
      name: '懐石料理',
    },
    images: [
      {
        key: 'restaurant/images/kyoboshi/main.jpg',
        originUrl: createPlaceholderImage('Kyoboshi'),
        remarks: 'Main dining room',
      },
    ],
    lunchTime: {
      from: '11:30',
      to: '14:00',
    },
    dinnerTime: {
      from: '17:30',
      to: '21:30',
    },
  },
  {
    name: 'すきやばし次郎本店',
    nameEn: 'Sukiyabashi Jiro Honten',
    phoneNumber: '03-3535-3600',
    address: {
      postalCode: '1040061',
      prefectureId: 13,
      prefectureName: '東京都',
      cityId: 13103,
      cityName: '中央区',
      wardId: 1310301,
      wardName: '銀座',
      buildingRoom: '4-2-15 Ginza Tsukamoto Sogyo Building B1F',
    },
    addressEn: 'B1F Tsukamoto Sogyo Building, 4-2-15 Ginza, Chuo-ku, Tokyo',
    accessInformation: '地下鉄銀座線「銀座駅」C6出口より徒歩2分',
    accessInformationEn: '2-minute walk from Ginza Station (Exit C6)',
    genre: {
      genreId: 2,
      name: '寿司',
    },
    images: [
      {
        key: 'restaurant/images/jiro/sushi-counter.jpg',
        originUrl: createPlaceholderImage('Jiro Sushi'),
        remarks: 'Sushi counter',
      },
    ],
    lunchTime: {
      from: '11:30',
      to: '14:00',
    },
    dinnerTime: {
      from: '17:00',
      to: '20:30',
    },
  },
  {
    name: 'でん',
    nameEn: 'Den',
    phoneNumber: '03-6447-5885',
    address: {
      postalCode: '1010051',
      prefectureId: 13,
      prefectureName: '東京都',
      cityId: 13102,
      cityName: '千代田区',
      wardId: 1310201,
      wardName: '神田神保町',
      buildingRoom: '2-3-18 Den Building 2F',
    },
    addressEn: '2F Den Building, 2-3-18 Kanda Jimbocho, Chiyoda-ku, Tokyo',
    accessInformation: '地下鉄半蔵門線「神保町駅」A5出口より徒歩3分',
    accessInformationEn: '3-minute walk from Jimbocho Station (Exit A5)',
    genre: {
      genreId: 3,
      name: 'モダン和食',
    },
    images: [
      {
        key: 'restaurant/images/den/interior.jpg',
        originUrl: createPlaceholderImage('Den'),
        remarks: 'Interior dining room',
      },
    ],
    lunchTime: null,
    dinnerTime: {
      from: '18:00',
      to: '23:00',
    },
  },
  {
    name: 'ナリサワ',
    nameEn: 'Narisawa',
    phoneNumber: '03-5785-0799',
    address: {
      postalCode: '1070062',
      prefectureId: 13,
      prefectureName: '東京都',
      cityId: 13107,
      cityName: '港区',
      wardId: 1310701,
      wardName: '南青山',
      buildingRoom: '2-6-15 Narisawa Building 1F',
    },
    addressEn: '1F Narisawa Building, 2-6-15 Minami-Aoyama, Minato-ku, Tokyo',
    accessInformation: '地下鉄銀座線「表参道駅」A4出口より徒歩4分',
    accessInformationEn: '4-minute walk from Omotesando Station (Exit A4)',
    genre: {
      genreId: 4,
      name: 'フレンチ',
    },
    images: [
      {
        key: 'restaurant/images/narisawa/plating.jpg',
        originUrl: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
        remarks: 'Signature dish presentation',
      },
    ],
    lunchTime: {
      from: '12:00',
      to: '15:00',
    },
    dinnerTime: {
      from: '18:00',
      to: '21:30',
    },
  },
  {
    name: '菊乃井本店',
    nameEn: 'Kikunoi Honten',
    phoneNumber: '************',
    address: {
      postalCode: '6058566',
      prefectureId: 26,
      prefectureName: '京都府',
      cityId: 26100,
      cityName: '京都市東山区',
      wardId: 2610001,
      wardName: '東山',
      buildingRoom: '459 Shimogawara-cho, Yasaka',
    },
    addressEn: '459 Shimogawara-cho, Yasaka, Higashiyama-ku, Kyoto',
    accessInformation: '京阪本線「祇園四条駅」1番出口より徒歩8分',
    accessInformationEn: '8-minute walk from Gion-Shijo Station (Exit 1)',
    genre: {
      genreId: 1,
      name: '懐石料理',
    },
    images: [
      {
        key: 'restaurant/images/kikunoi/garden.jpg',
        originUrl: 'https://images.unsplash.com/photo-1559339352-11d035aa65de?w=400&h=300&fit=crop',
        remarks: 'Traditional garden view',
      },
    ],
    lunchTime: {
      from: '12:00',
      to: '14:30',
    },
    dinnerTime: {
      from: '17:00',
      to: '19:30',
    },
  },
  {
    name: '石かわ',
    nameEn: 'Ishikawa',
    phoneNumber: '03-5225-0173',
    address: {
      postalCode: '1620825',
      prefectureId: 13,
      prefectureName: '東京都',
      cityId: 13104,
      cityName: '新宿区',
      wardId: 1310401,
      wardName: '神楽坂',
      buildingRoom: '5-37 Kagurazaka, Ishikawa Building 1F',
    },
    addressEn: '1F Ishikawa Building, 5-37 Kagurazaka, Shinjuku-ku, Tokyo',
    accessInformation: 'JR総武線「飯田橋駅」西口より徒歩5分',
    accessInformationEn: '5-minute walk from Iidabashi Station (West Exit)',
    genre: {
      genreId: 1,
      name: '懐石料理',
    },
    images: [
      {
        key: 'restaurant/images/ishikawa/counter.jpg',
        originUrl: 'https://images.unsplash.com/photo-1604603471102-68b2838bed51?w=400&h=300&fit=crop',
        remarks: 'Chef counter seating',
      },
    ],
    lunchTime: null,
    dinnerTime: {
      from: '17:30',
      to: '20:00',
    },
  },
  {
    name: '吉野家本店',
    nameEn: 'Yoshinoya Honten',
    phoneNumber: '03-3541-3673',
    address: {
      postalCode: '1040045',
      prefectureId: 13,
      prefectureName: '東京都',
      cityId: 13103,
      cityName: '中央区',
      wardId: 1310302,
      wardName: '築地',
      buildingRoom: '4-9-9 Tsukiji Market Area Building 2F',
    },
    addressEn: '2F Market Area Building, 4-9-9 Tsukiji, Chuo-ku, Tokyo',
    accessInformation: '地下鉄日比谷線「築地駅」A1出口より徒歩5分',
    accessInformationEn: '5-minute walk from Tsukiji Station (Exit A1)',
    genre: {
      genreId: 5,
      name: '日本料理',
    },
    images: [
      {
        key: 'restaurant/images/yoshinoya/traditional.jpg',
        originUrl: 'https://images.unsplash.com/photo-1553909489-cd47e0ef937f?w=400&h=300&fit=crop',
        remarks: 'Traditional dining area',
      },
    ],
    lunchTime: {
      from: '11:00',
      to: '14:00',
    },
    dinnerTime: {
      from: '17:00',
      to: '21:00',
    },
  },
  {
    name: '天ぷら近藤',
    nameEn: 'Tempura Kondo',
    phoneNumber: '03-5568-0923',
    address: {
      postalCode: '1040061',
      prefectureId: 13,
      prefectureName: '東京都',
      cityId: 13103,
      cityName: '中央区',
      wardId: 1310301,
      wardName: '銀座',
      buildingRoom: '5-5-13 Ginza Kondo Building 9F',
    },
    addressEn: '9F Kondo Building, 5-5-13 Ginza, Chuo-ku, Tokyo',
    accessInformation: '地下鉄銀座線「銀座駅」C9出口より徒歩1分',
    accessInformationEn: '1-minute walk from Ginza Station (Exit C9)',
    genre: {
      genreId: 6,
      name: '天ぷら',
    },
    images: [
      {
        key: 'restaurant/images/kondo/tempura.jpg',
        originUrl: 'https://images.unsplash.com/photo-1571003123894-1f0594d2b5d9?w=400&h=300&fit=crop',
        remarks: 'Fresh tempura preparation',
      },
    ],
    lunchTime: {
      from: '11:30',
      to: '14:30',
    },
    dinnerTime: {
      from: '17:00',
      to: '20:30',
    },
  },
  {
    name: '鮨さいとう',
    nameEn: 'Sushi Saito',
    phoneNumber: '03-3589-4412',
    address: {
      postalCode: '1066047',
      prefectureId: 13,
      prefectureName: '東京都',
      cityId: 13103,
      cityName: '港区',
      wardId: 1310601,
      wardName: '赤坂',
      buildingRoom: '1-4-8 Akasaka Saito Building 1F',
    },
    addressEn: '1F Saito Building, 1-4-8 Akasaka, Minato-ku, Tokyo',
    accessInformation: '地下鉄千代田線「赤坂駅」2番出口より徒歩3分',
    accessInformationEn: '3-minute walk from Akasaka Station (Exit 2)',
    genre: {
      genreId: 2,
      name: '寿司',
    },
    images: [
      {
        key: 'restaurant/images/saito/omakase.jpg',
        originUrl: 'https://images.unsplash.com/photo-1617196034796-73dfa7b1fd56?w=400&h=300&fit=crop',
        remarks: 'Omakase course',
      },
    ],
    lunchTime: {
      from: '12:00',
      to: '14:00',
    },
    dinnerTime: {
      from: '18:00',
      to: '20:00',
    },
  },
  {
    name: 'フレンチレストラン　ラミティエ',
    nameEn: 'French Restaurant L\'Amitie',
    phoneNumber: '03-5413-3270',
    address: {
      postalCode: '1500002',
      prefectureId: 13,
      prefectureName: '東京都',
      cityId: 13113,
      cityName: '渋谷区',
      wardId: 1311301,
      wardName: '渋谷',
      buildingRoom: '1-15-3 Shibuya Amitie Building 12F',
    },
    addressEn: '12F Amitie Building, 1-15-3 Shibuya, Shibuya-ku, Tokyo',
    accessInformation: 'JR山手線「渋谷駅」ハチ公口より徒歩7分',
    accessInformationEn: '7-minute walk from Shibuya Station (Hachiko Exit)',
    genre: {
      genreId: 4,
      name: 'フレンチ',
    },
    images: [
      {
        key: 'restaurant/images/lamitie/cityview.jpg',
        originUrl: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400&h=300&fit=crop',
        remarks: 'City view dining',
      },
    ],
    lunchTime: {
      from: '11:30',
      to: '15:00',
    },
    dinnerTime: {
      from: '17:30',
      to: '22:00',
    },
  },
  {
    name: '炭火焼肉　牛角',
    nameEn: 'Charcoal BBQ Gyukaku',
    phoneNumber: '03-5456-9829',
    address: {
      postalCode: '1500042',
      prefectureId: 13,
      prefectureName: '東京都',
      cityId: 13113,
      cityName: '渋谷区',
      wardId: 1311302,
      wardName: '宇田川町',
      buildingRoom: '25-1 Udagawa-cho Gyukaku Building 3F',
    },
    addressEn: '3F Gyukaku Building, 25-1 Udagawa-cho, Shibuya-ku, Tokyo',
    accessInformation: 'JR山手線「渋谷駅」センター街口より徒歩5分',
    accessInformationEn: '5-minute walk from Shibuya Station (Center Gai Exit)',
    genre: {
      genreId: 7,
      name: '焼肉',
    },
    images: [
      {
        key: 'restaurant/images/gyukaku/grill.jpg',
        originUrl: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=400&h=300&fit=crop',
        remarks: 'Premium wagyu grilling',
      },
    ],
    lunchTime: {
      from: '11:30',
      to: '15:00',
    },
    dinnerTime: {
      from: '17:00',
      to: '23:00',
    },
  },
  {
    name: 'イタリアン　ダ・ジョルジョ',
    nameEn: 'Italian Da Giorgio',
    phoneNumber: '03-6234-7890',
    address: {
      postalCode: '1070052',
      prefectureId: 13,
      prefectureName: '東京都',
      cityId: 13107,
      cityName: '港区',
      wardId: 1310702,
      wardName: '赤坂',
      buildingRoom: '3-2-11 Akasaka Giorgio Building 2F',
    },
    addressEn: '2F Giorgio Building, 3-2-11 Akasaka, Minato-ku, Tokyo',
    accessInformation: '地下鉄千代田線「赤坂駅」1番出口より徒歩4分',
    accessInformationEn: '4-minute walk from Akasaka Station (Exit 1)',
    genre: {
      genreId: 8,
      name: 'イタリアン',
    },
    images: [
      {
        key: 'restaurant/images/giorgio/pasta.jpg',
        originUrl: 'https://images.unsplash.com/photo-1551183053-bf91a1d81141?w=400&h=300&fit=crop',
        remarks: 'Handmade pasta preparation',
      },
    ],
    lunchTime: {
      from: '11:30',
      to: '14:30',
    },
    dinnerTime: {
      from: '18:00',
      to: '22:00',
    },
  },
  {
    name: '回転寿司　まぐろ亭',
    nameEn: 'Conveyor Sushi Maguro-tei',
    phoneNumber: '03-3987-6543',
    address: {
      postalCode: '1710022',
      prefectureId: 13,
      prefectureName: '東京都',
      cityId: 13116,
      cityName: '豊島区',
      wardId: 1311601,
      wardName: '南池袋',
      buildingRoom: '1-28-2 Minami-Ikebukuro Maguro Building 1F',
    },
    addressEn: '1F Maguro Building, 1-28-2 Minami-Ikebukuro, Toshima-ku, Tokyo',
    accessInformation: 'JR山手線「池袋駅」東口より徒歩3分',
    accessInformationEn: '3-minute walk from Ikebukuro Station (East Exit)',
    genre: {
      genreId: 2,
      name: '寿司',
    },
    images: [
      {
        key: 'restaurant/images/maguro/conveyor.jpg',
        originUrl: 'https://images.unsplash.com/photo-1617196034183-421b4917c92d?w=400&h=300&fit=crop',
        remarks: 'Fresh sushi on conveyor belt',
      },
    ],
    lunchTime: {
      from: '11:00',
      to: '15:00',
    },
    dinnerTime: {
      from: '17:00',
      to: '22:00',
    },
  },
  {
    name: 'そば処　田舎庵',
    nameEn: 'Soba Dokoro Inaka-an',
    phoneNumber: '03-3251-4567',
    address: {
      postalCode: '1010047',
      prefectureId: 13,
      prefectureName: '東京都',
      cityId: 13102,
      cityName: '千代田区',
      wardId: 1310203,
      wardName: '内神田',
      buildingRoom: '2-15-8 Uchi-Kanda Inaka Building 1F',
    },
    addressEn: '1F Inaka Building, 2-15-8 Uchi-Kanda, Chiyoda-ku, Tokyo',
    accessInformation: 'JR中央線「神田駅」北口より徒歩6分',
    accessInformationEn: '6-minute walk from Kanda Station (North Exit)',
    genre: {
      genreId: 9,
      name: 'そば',
    },
    images: [
      {
        key: 'restaurant/images/inaka/soba.jpg',
        originUrl: 'https://images.unsplash.com/photo-1555126634-323283e090fa?w=400&h=300&fit=crop',
        remarks: 'Handmade buckwheat noodles',
      },
    ],
    lunchTime: {
      from: '11:30',
      to: '14:00',
    },
    dinnerTime: {
      from: '17:30',
      to: '20:30',
    },
  },
  {
    name: '中華料理　金龍飯店',
    nameEn: 'Chinese Restaurant Golden Dragon',
    phoneNumber: '03-5321-9876',
    address: {
      postalCode: '1600023',
      prefectureId: 13,
      prefectureName: '東京都',
      cityId: 13104,
      cityName: '新宿区',
      wardId: 1310402,
      wardName: '西新宿',
      buildingRoom: '1-1-3 Nishi-Shinjuku Dragon Tower 8F',
    },
    addressEn: '8F Dragon Tower, 1-1-3 Nishi-Shinjuku, Shinjuku-ku, Tokyo',
    accessInformation: 'JR山手線「新宿駅」南口より徒歩8分',
    accessInformationEn: '8-minute walk from Shinjuku Station (South Exit)',
    genre: {
      genreId: 10,
      name: '中華料理',
    },
    images: [
      {
        key: 'restaurant/images/golden-dragon/dimsum.jpg',
        originUrl: 'https://images.unsplash.com/photo-1526318896980-cf78c088247c?w=400&h=300&fit=crop',
        remarks: 'Traditional dim sum selection',
      },
    ],
    lunchTime: {
      from: '11:30',
      to: '15:00',
    },
    dinnerTime: {
      from: '17:30',
      to: '22:30',
    },
  },
];

// Total count for pagination simulation
export const TOTAL_MOCK_RESTAURANTS = 50; // Simulate more restaurants than we have in the array
