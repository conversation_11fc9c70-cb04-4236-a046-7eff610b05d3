import type { RestaurantApiResponse, RestaurantQueryParams } from 'models/restaurant/types';
import { mockRestaurants, TOTAL_MOCK_RESTAURANTS } from './restaurant-data';

/**
 * Mock restaurant service that simulates the /customer/restaurants API endpoint
 * This service handles pagination, filtering, and returns data in the exact format
 * expected by the frontend components.
 */
export class MockRestaurantService {
  /**
   * Simulates the GET /customer/restaurants API call
   * @param params Query parameters (page, limit, fromDate, toDate)
   * @returns Promise<RestaurantApiResponse> - Formatted API response
   */
  static async getRestaurants(params: RestaurantQueryParams = {}): Promise<RestaurantApiResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const {
      page = 1,
      limit = 10,
      fromDate,
      toDate,
    } = params;

    // For this mock, we'll ignore date filtering and just use all restaurants
    // In a real implementation, you would filter based on fromDate/toDate
    const filteredRestaurants = [...mockRestaurants];

    // Simulate date filtering (placeholder logic)
    if (fromDate || toDate) {
      // In a real implementation, you would filter restaurants based on availability
      // For now, we'll just add a comment about date filtering
      // eslint-disable-next-line no-console
      console.log('Mock: Date filtering requested', { fromDate, toDate });
    }

    // Cycle through the mock data to simulate more restaurants
    const extendedRestaurants = [];
    for (let i = 0; i < TOTAL_MOCK_RESTAURANTS; i++) {
      const restaurant = filteredRestaurants[i % filteredRestaurants.length];
      if (restaurant) {
        extendedRestaurants.push({
          ...restaurant,
          // Add slight variations to make each entry unique
          name: `${restaurant.name}${i >= filteredRestaurants.length ? ` (${Math.floor(i / filteredRestaurants.length) + 1})` : ''}`,
          nameEn: `${restaurant.nameEn}${i >= filteredRestaurants.length ? ` Branch ${Math.floor(i / filteredRestaurants.length) + 1}` : ''}`,
        });
      }
    }

    // Implement pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedRestaurants = extendedRestaurants.slice(startIndex, endIndex);

    // Calculate pagination metadata
    const totalPages = Math.ceil(TOTAL_MOCK_RESTAURANTS / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;
    const nextPage = hasNextPage ? page + 1 : null;

    // Return response in exact API format
    return {
      data: {
        docs: paginatedRestaurants,
        totalDocs: TOTAL_MOCK_RESTAURANTS,
        limit,
        page,
        hasNextPage,
        hasPrevPage,
        totalPages,
        nextPage,
      },
    };
  }

  /**
   * Utility method to check if mock mode is enabled
   */
  static isMockEnabled(): boolean {
    return process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_MOCK_API !== 'false';
  }
}

export default MockRestaurantService;
