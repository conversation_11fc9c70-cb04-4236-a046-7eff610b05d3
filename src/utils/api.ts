import type { AxiosHeaders } from 'axios';
import type { <PERSON><PERSON><PERSON><PERSON>, WebCookie } from './types';
import axios from 'axios';

import get from 'lodash/get';
import authQuery from 'models/auth';
import ApiInterceptor from '../mocks/api-interceptor';
import { COOKIE_TOKEN_KEY } from './constants';
import { getCookie, setCookie } from './server-cookies';

let isRefreshing = false;
let failedQueue: {
  resolve: (value: unknown) => void;
  reject: (reason?: any) => void;
}[] = [];

const processQueue = (error: unknown, token = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
};

const api = axios.create({
  baseURL: `${process.env.API_SERVER_BASE_URL}`,
  headers: {
    'Content-Type': 'application/json',
  },
});

api.interceptors.request.use(async (config) => {
  const webCookie = await getCookie<WebCookie>(COOKIE_TOKEN_KEY);
  if (config.headers) {
    if (webCookie?.accessToken && !config.headers.Authorization) {
      (config.headers as AxiosHeaders).set(
        'Authorization',
        `Bearer ${webCookie?.accessToken}`,
      );
    }
    (config.headers as AxiosHeaders).set(
      'Accept-Timezone',
      Intl.DateTimeFormat().resolvedOptions().timeZone,
    );
  }
  const requestConfig = { ...config };
  if (requestConfig.method?.toLowerCase() === 'get' && requestConfig.data) {
    requestConfig.params = requestConfig.data;
    delete requestConfig.data;
  }
  return requestConfig;
});

api.interceptors.response.use(
  (response) => {
    if (response.data.data) {
      return response.data;
    }
    return {
      data: response.data,
    };
  },
  async (error) => {
    if (!error) {
      return Promise.reject(new Error('No internet connection.'));
    }
    const webCookie = await getCookie<WebCookie>(COOKIE_TOKEN_KEY);
    const originalRequest = error.config;
    const refreshToken = webCookie?.refreshToken;

    if (
      error.response?.status === 401
      && refreshToken
      && !originalRequest._retry
    ) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return api(originalRequest);
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      return new Promise((resolve, reject) => {
        api
          .post(
            authQuery.refreshToken().apiUrl,
            {},
            {
              headers: {
                Authorization: `Bearer ${refreshToken}`,
              },
            },
          )
          .then(({ data }) => {
            setCookie(COOKIE_TOKEN_KEY, JSON.stringify(data), {
              httpOnly: true,
              maxAge: 7776000000,
              sameSite: 'strict',
            });
            api.defaults.headers.common.Authorization = `Bearer ${data.accessToken}`;
            originalRequest.headers.Authorization = `Bearer ${data.accessToken}`;
            processQueue(null, data.accessToken);
            resolve(api(originalRequest));
          })
          .catch((err: IError) => {
            processQueue(err, null);
            if (err.statusCode === 401) {
              window.location.href = '/';
            }
            reject(err);
          })
          .finally(() => {
            isRefreshing = false;
          });
      });
    }
    // eslint-disable-next-line prefer-promise-reject-errors
    return Promise.reject({
      data: get(error.response, 'data.data') || get(error.response, 'data'),
      error: get(error.response, 'data.error'),
      message: get(error.response, 'data.message', error.message),
      statusCode:
        get(error.response, 'data.data.result.statusCode')
        || get(error.response, 'status'),
    });
  },
);

// Install mock interceptor for development (must be installed after auth interceptors)
if (process.env.NODE_ENV === 'development') {
  ApiInterceptor.install(api);
}

export default api;
