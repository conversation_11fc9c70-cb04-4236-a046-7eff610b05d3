import type { Metadata } from 'next/types';
import { Container, Stack, Text, Title } from '@mantine/core';

export const metadata: Metadata = {
  title: 'FAQ',
  description: 'Frequently Asked Questions',
};

const FAQ = () => {
  return (
    <Container size="lg" py={40}>
      <Stack gap={24}>
        <Title order={1} size="h1" fw={700}>
          Frequently Asked Questions
        </Title>
        <Text size="lg" c="gray.7">
          Find answers to common questions about our services.
        </Text>
        <Text c="gray.6">
          This page will contain frequently asked questions and their answers to help users understand our platform better.
        </Text>
      </Stack>
    </Container>
  );
};

export default FAQ;
