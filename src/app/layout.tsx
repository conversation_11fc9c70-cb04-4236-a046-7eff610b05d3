import type { Metadata, Viewport } from 'next';
import type { ReactNode } from 'react';
import { MFProvider } from '@c2c-platform/mfe';
import { ColorSchemeScript, MantineProvider } from '@mantine/core';
import { Notifications } from '@mantine/notifications';

import Layout from 'components/Layout';
import { cookies } from 'next/headers';
import BroadcastProvider from 'providers/BroadcastProvider';
import MantineDatesProvider from 'providers/MantineDatesProvider';
import MantineModalsProvider from 'providers/MantineModalsProvider';
import QueryProvider from 'providers/QueryProvider';
import React from 'react';
import theme from 'theme';
import { COOKIE_USER_DATA_KEY } from 'utils/constants';
import configRemote from '../../config.DEV.json';
import '@mantine/core/styles.css';
import '@mantine/dates/styles.css';
import '@mantine/notifications/styles.css';
import './global.css';
import 'utils/yup';
// const CookieConsent = dynamic(() => import('components/CookieConsent'));

export const metadata: Metadata = {
  title: 'Ohako',
  description: 'Ohako customer',
  robots: {
    index: process.env.DEPLOY_ENV === 'production',
    follow: process.env.DEPLOY_ENV === 'production',
  },
};

export const viewport: Viewport = {
  maximumScale: 1,
  initialScale: 1,
  width: 'device-width',
  userScalable: false,
};

export default async function RootLayout({
  children,
}: {
  children: ReactNode;
}) {
  const cookieStore = await cookies();
  const userData = cookieStore.get(COOKIE_USER_DATA_KEY)?.value;

  return (
    <html lang="en" style={{ fontSize: 16 }}>
      <head>
        <ColorSchemeScript />
        <link href="/favicon.ico" rel="shortcut icon" />
        <link
          href="https://fonts.googleapis.com/css2?family=Merriweather:wght@300;400;700;900&display=swap"
          rel="stylesheet"
        />
      </head>
      <body suppressHydrationWarning>
        <MFProvider remotes={configRemote}>
          <MantineProvider theme={theme}>
            <MantineModalsProvider>
              <MantineDatesProvider>
                <QueryProvider>
                  <Notifications containerWidth={440} />
                  <BroadcastProvider />
                  <Layout loggedIn={!!userData}>{children}</Layout>
                  {/* <CookieConsent /> */}
                </QueryProvider>
              </MantineDatesProvider>
            </MantineModalsProvider>
          </MantineProvider>
        </MFProvider>
      </body>
    </html>
  );
}
