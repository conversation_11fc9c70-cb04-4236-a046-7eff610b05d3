'use client';

import type { SearchFilters } from 'components/Restaurant';
import type { Restaurant, RestaurantApiResponse } from 'models/restaurant/types';
import { Al<PERSON>, Center, Container, Loader, Stack, Text, Title } from '@mantine/core';
import { Pagination, RestaurantCard, SearchSection } from 'components/Restaurant';
import useFetch from 'hooks/useFetch';
import { MockRestaurantService } from 'mocks';
import restaurantQuery from 'models/restaurant';
import { useCallback, useEffect, useState } from 'react';

const ITEMS_PER_PAGE = 10;

const RestaurantPage = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    searchQuery: '',
    startDate: null,
    endDate: null,
  });
  const [mockData, setMockData] = useState<RestaurantApiResponse | null>(null);
  const [useMockFallback, setUseMockFallback] = useState(false);

  // Check if we're in mock mode
  const isMockMode = process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_MOCK_API !== 'false';

  // Prepare API query parameters
  const queryParams = {
    page: currentPage,
    limit: ITEMS_PER_PAGE,
    ...(searchFilters.startDate && { fromDate: searchFilters.startDate.toISOString() }),
    ...(searchFilters.endDate && { toDate: searchFilters.endDate.toISOString() }),
  };

  // Fetch restaurants using the API (this should be intercepted by mock in development)
  const {
    data: apiResponse,
    isLoading: isApiLoading,
    error: apiError,
    refetch,
  } = useFetch<RestaurantApiResponse>({
    queryKey: ['restaurants', currentPage, searchFilters],
    ...restaurantQuery.getRestaurants(queryParams),
    enabled: !useMockFallback,
  });

  // Load mock data as fallback if API fails in development
  useEffect(() => {
    if (isMockMode && (apiError || (!isApiLoading && !apiResponse))) {
      // eslint-disable-next-line no-console
      console.log('API failed, loading mock data fallback...');
      setUseMockFallback(true);

      MockRestaurantService.getRestaurants(queryParams)
        .then((response) => {
          setMockData(response);
        })
        .catch((error) => {
          console.error('Mock service also failed:', error);
        });
    }
  }, [isMockMode, apiError, isApiLoading, apiResponse, currentPage, searchFilters]);

  // Use either API data or mock data
  const finalResponse = useMockFallback ? mockData : apiResponse;
  const isLoading = useMockFallback ? !mockData : isApiLoading;
  const error = useMockFallback ? null : apiError;

  const restaurants = finalResponse?.data?.docs || [];
  const totalItems = finalResponse?.data?.totalDocs || 0;
  const totalPages = finalResponse?.data?.totalPages || 0;

  // Filter restaurants by search query if needed (client-side filtering)
  const filteredRestaurants = searchFilters.searchQuery
    ? restaurants.filter(restaurant =>
        restaurant.nameEn?.toLowerCase().includes(searchFilters.searchQuery.toLowerCase())
        || restaurant.name.toLowerCase().includes(searchFilters.searchQuery.toLowerCase())
        || restaurant.genre.name.toLowerCase().includes(searchFilters.searchQuery.toLowerCase())
        || restaurant.accessInformationEn?.toLowerCase().includes(searchFilters.searchQuery.toLowerCase())
        || restaurant.accessInformation.toLowerCase().includes(searchFilters.searchQuery.toLowerCase()),
      )
    : restaurants;

  const handleSearch = useCallback((filters: SearchFilters) => {
    setSearchFilters(filters);
    setCurrentPage(1); // Reset to first page on new search
  }, []);

  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
    // Smooth scroll to top of the page
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  const handleRestaurantClick = useCallback((restaurant: Restaurant) => {
    // Handle restaurant selection - navigate to detail page
    // eslint-disable-next-line no-console
    console.log('Selected restaurant:', restaurant);
    // router.push(`/restaurant/${restaurant.id}`);
  }, []);

  // Refetch when search filters or page changes (only for API calls)
  useEffect(() => {
    if (!useMockFallback) {
      refetch();
    } else {
      // For mock data, reload the mock service
      MockRestaurantService.getRestaurants(queryParams)
        .then((response) => {
          setMockData(response);
        })
        .catch((error) => {
          console.error('Mock service failed on refetch:', error);
        });
    }
  }, [currentPage, searchFilters, refetch, useMockFallback]);

  // Debug logging
  useEffect(() => {
    // eslint-disable-next-line no-console
    console.log('Mock mode:', isMockMode);
    // eslint-disable-next-line no-console
    console.log('Using mock fallback:', useMockFallback);
    // eslint-disable-next-line no-console
    console.log('API Response:', apiResponse);
    // eslint-disable-next-line no-console
    console.log('Mock Data:', mockData);
    // eslint-disable-next-line no-console
    console.log('Final Response:', finalResponse);
    // eslint-disable-next-line no-console
    console.log('Error:', error);
    // eslint-disable-next-line no-console
    console.log('Restaurants:', restaurants);
  }, [isMockMode, useMockFallback, apiResponse, mockData, finalResponse, error, restaurants]);

  // Handle error state (only show error if not using mock fallback)
  if (error && !useMockFallback) {
    return (
      <Container size="lg" py={40}>
        <Center py={60}>
          <Stack align="center" gap={16}>
            <Text
              size="lg"
              style={{
                color: '#E53E3E',
                fontSize: '18px',
                fontWeight: 500,
              }}
            >
              Error loading restaurants
            </Text>
            <Text
              size="md"
              style={{
                color: '#BABABA',
                fontSize: '14px',
                fontWeight: 400,
              }}
            >
              {(error as any)?.message || 'Please try again later'}
            </Text>
            {isMockMode && (
              <Alert color="orange" title="Loading Mock Data">
                API failed. Switching to mock data fallback...
              </Alert>
            )}
          </Stack>
        </Center>
      </Container>
    );
  }

  return (
    <Container size="lg" py={40}>
      <Stack gap={32}>

        {/* Search Section */}
        <SearchSection onSearch={handleSearch} isLoading={isLoading} />

        {/* Page Title */}
        <div style={{
          width: '100%',
          maxWidth: '704px',
          display: 'flex',
          margin: '0 auto',
          flexDirection: 'column',
          gap: '8px',
        }}
        >
          <Title
            order={1}
            size="h1"
            style={{
              color: '#111D2F',
              fontSize: '32px',
              fontWeight: 700,
              lineHeight: '1.3',
              marginBottom: '8px',
            }}
          >
            Discover Japan's Top Fine Dining
          </Title>
          <Text
            size="lg"
            style={{
              color: '#828282',
              fontSize: '16px',
              fontWeight: 400,
              lineHeight: '1.5',
            }}
          >
            Experience the finest culinary traditions and innovative flavors across Japan
          </Text>
        </div>

        {/* Restaurant Listings */}
        {isLoading
          ? (
              <Center py={60}>
                <Loader color="primary.6" size="lg" />
              </Center>
            )
          : filteredRestaurants.length > 0
            ? (
                <Stack gap={20}>
                  {filteredRestaurants.map((restaurant, index) => (
                    <RestaurantCard
                      key={`${restaurant.name}-${index}`}
                      restaurant={restaurant}
                      onClick={handleRestaurantClick}
                    />
                  ))}
                </Stack>
              )
            : (
                <Center py={60}>
                  <Stack align="center" gap={16}>
                    <Text
                      size="lg"
                      style={{
                        color: '#828282',
                        fontSize: '18px',
                        fontWeight: 500,
                      }}
                    >
                      No restaurants found
                    </Text>
                    <Text
                      size="md"
                      style={{
                        color: '#BABABA',
                        fontSize: '14px',
                        fontWeight: 400,
                      }}
                    >
                      Try adjusting your search criteria to find more results
                    </Text>
                    {isMockMode && (
                      <Text size="sm" c="dimmed">
                        {useMockFallback
                          ? 'Using mock data fallback. If no data shows, check console for details.'
                          : 'Mock mode is active but no data is loading. Check console for details.'}
                      </Text>
                    )}
                  </Stack>
                </Center>
              )}

        {/* Pagination */}
        {!isLoading && filteredRestaurants.length > 0 && totalPages > 1 && (
          <Pagination
            currentPage={currentPage}
            itemsPerPage={ITEMS_PER_PAGE}
            totalItems={totalItems}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        )}
      </Stack>
    </Container>
  );
};

export default RestaurantPage;
