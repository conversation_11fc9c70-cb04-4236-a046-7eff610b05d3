import type { Metadata } from 'next/types';
import { Container, Stack, Text, Title } from '@mantine/core';

export const metadata: Metadata = {
  title: 'Etiquette',
  description: 'Dining etiquette and guidelines',
};

const Etiquette = () => {
  return (
    <Container size="lg" py={40}>
      <Stack gap={24}>
        <Title order={1} size="h1" fw={700}>
          Etiquette
        </Title>
        <Text size="lg" c="gray.7">
          Learn about proper dining etiquette and restaurant guidelines.
        </Text>
        <Text c="gray.6">
          This page will provide information about dining etiquette, restaurant manners, and cultural guidelines for a better dining experience.
        </Text>
      </Stack>
    </Container>
  );
};

export default Etiquette;
