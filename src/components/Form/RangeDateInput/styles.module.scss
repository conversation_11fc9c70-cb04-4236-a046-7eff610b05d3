.container {
  position: relative;
  width: 100%;
}

.wrapper {
  position: relative;
}

.input {
  width: 100%;
  height: 48px;
  padding: 12px 48px 12px 16px !important;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: #111D2F;
  background: #FFFFFF;
  border: 1px solid #E5E5E5;
  border-radius: 8px;
  transition: all 0.2s ease;
  text-align: left;

  &::placeholder {
    color: #BABABA;
    font-weight: 400;
  }

  &:focus {
    border-color: #007BFF;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
    outline: none;
  }

  &:hover:not(:focus) {
    border-color: #CCCCCC;
  }

  &:disabled {
    background-color: #F8F9FA;
    color: #BABABA;
    cursor: not-allowed;
  }
}

.calendarIcon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #828282 !important;
  pointer-events: none;
  z-index: 1;

  path {
    stroke: #828282 !important;
  }
}

// Mobile responsive
@media (max-width: 768px) {
  .input {
    height: 44px;
    font-size: 14px;
    padding: 10px 44px 10px 14px !important;
  }

  .calendarIcon {
    right: 14px;
    width: 18px;
    height: 18px;
  }
}

// Remove focus state color changes for calendar icon
.container:focus-within {
  .calendarIcon {
    color: #828282 !important;
    
    path {
      stroke: #828282 !important;
    }
  }
} 