'use client';

import type { DatesRangeValue } from '@mantine/dates';
import CalendarIcon from '@icons/icon-calendar.svg';
import { DatePickerInput } from '@mantine/dates';
import { clsx } from 'clsx';

import classes from './styles.module.scss';

type RangeDateInputProps = {
  value: DatesRangeValue;
  onChange: (value: DatesRangeValue) => void;
  placeholder?: [string, string];
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  clearable?: boolean;
};

const RangeDateInput = ({
  value,
  onChange,
  placeholder = ['Start date', 'End date'],
  disabled = false,
  size = 'lg',
  className,
  clearable = true,
}: RangeDateInputProps) => {
  return (
    <div className={clsx(classes.container, className)}>
      <DatePickerInput
        type="range"
        value={value}
        onChange={onChange}
        placeholder={placeholder.join(' → ')}
        size={size}
        disabled={disabled}
        clearable={clearable}
        valueFormat="MMMM DD, YYYY"
        classNames={{
          input: classes.input,
          wrapper: classes.wrapper,
        }}
        rightSection={(
          <CalendarIcon
            className={classes.calendarIcon}
            width={20}
            height={20}
          />
        )}
        rightSectionPointerEvents="none"
      />
    </div>
  );
};

export default RangeDateInput;
