'use client';

import type { DatesRangeValue } from '@mantine/dates';
import SearchIcon from '@icons/icon-search.svg';
import { Button, Flex, Stack, Text, useMatches } from '@mantine/core';
import { RangeDateInput } from 'components/Form';
import { useState } from 'react';

import classes from './styles.module.scss';

export type SearchFilters = {
  searchQuery: string;
  startDate: Date | null;
  endDate: Date | null;
};

type SearchSectionProps = {
  onSearch: (filters: SearchFilters) => void;
  isLoading?: boolean;
};

const SearchSection = ({ onSearch, isLoading }: SearchSectionProps) => {
  const [dateRange, setDateRange] = useState<DatesRangeValue>([null, null]);

  const isMobile = useMatches({
    base: true,
    sm: false,
  });

  const handleSearch = () => {
    onSearch({
      searchQuery: '',
      startDate: dateRange[0],
      endDate: dateRange[1],
    });
  };

  return (
    <div className={classes.container}>
      {isMobile
        ? (
          // Mobile: Vertical Stack
            <Stack gap={16}>
              <Stack gap={8}>
                <Text size="sm" c="gray.7" fw={500}>
                  Reservation Date
                </Text>
                <RangeDateInput
                  value={dateRange}
                  onChange={setDateRange}
                  placeholder={['June 19, 2025', 'July 19, 2025']}
                  size="lg"
                />
              </Stack>

              <Flex gap={8}>

                <Button
                  className={classes.searchButton}
                  fullWidth
                  loading={isLoading}
                  size="lg"
                  onClick={handleSearch}
                >
                  Search
                </Button>
              </Flex>
            </Stack>
          )
        : (
          // Desktop: Horizontal Layout
            <Stack gap={16}>
              <Flex align="flex-end" gap={16}>
                <Stack gap={8} style={{ flex: 1 }}>
                  <Text size="sm" c="gray.7" fw={500}>
                    Reservation Date
                  </Text>
                  <RangeDateInput
                    value={dateRange}
                    onChange={setDateRange}
                    size="lg"
                  />
                </Stack>

                <Flex gap={8}>

                  <Button
                    className={classes.searchButton}
                    loading={isLoading}
                    size="lg"
                    leftSection={<SearchIcon width={11} height={11} />}
                    onClick={handleSearch}
                  >
                    Search
                  </Button>
                </Flex>
              </Flex>
            </Stack>
          )}
    </div>
  );
};

export default SearchSection;
