.container {
  background-color: var(--mantine-color-gray-0);
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 32px;
  width: 704px;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
}

@media (max-width: 768px) {
  .container {
    width: 100%;
    margin: 0 0 32px 0;
    padding: 16px;
  }
}

.searchInput {
  --input-height: 48px;
  --input-bd: var(--mantine-color-gray-5);
  --input-color: var(--mantine-color-gray-8);
  --input-bg: var(--mantine-color-white);
  --input-bd-focus: var(--mantine-color-primary-6);
  --input-placeholder-color: var(--mantine-color-gray-4);
  --input-section-color: var(--mantine-color-gray-5);

  border-radius: 6px;
  font-size: 14px;

  &:focus {
    box-shadow: 0px 0px 0px 3px var(--mantine-color-primary-2);
  }
}

.searchButton {
  --button-bg: var(--mantine-color-primary-6);
  --button-color: var(--mantine-color-white);
  --button-bd: var(--mantine-color-primary-6);
  --button-hover: var(--mantine-color-primary-7);
  --button-hover-bd: var(--mantine-color-primary-7);
  --button-active: var(--mantine-color-primary-8);
  --button-active-bd: var(--mantine-color-primary-8);

  border-radius: 6px;
  font-weight: 500;
  min-width: 100px;
}

.clearButton {
  --button-bg: transparent;
  --button-color: var(--mantine-color-gray-7);
  --button-bd: var(--mantine-color-gray-5);
  --button-hover: var(--mantine-color-gray-1);
  --button-hover-bd: var(--mantine-color-gray-6);
  --button-active: var(--mantine-color-gray-2);
  --button-active-bd: var(--mantine-color-gray-7);

  border-radius: 6px;
  font-weight: 500;
  min-width: 80px;
}
