'use client';

import type { Restaurant } from 'models/restaurant/types';
import GenreIcon from '@icons/icon-genre.svg';
import LocationIcon from '@icons/icon-location.svg';
import IConMoon from '@icons/icon-moon.svg';
import IconSun from '@icons/icon-sun.svg';
import WalkmanIcon from '@icons/icon-walk-distance.svg';
import { Card, Flex, Group, Stack, Text, ThemeIcon, useMatches } from '@mantine/core';
import Image from 'next/image';

import { currencyFormat } from 'utils/helpers';
import classes from './styles.module.scss';

// Legacy type for backward compatibility (can be removed later)
export type LegacyRestaurant = {
  id: string;
  name: string;
  nameJp?: string;
  image: string;
  location: string;
  cuisine: string;
  access: string;
  mealTimes: {
    lunch: boolean;
    dinner: boolean;
  };
  priceRange: {
    lunch?: string;
    dinner?: string;
  };
  rating?: number;
};

export type { Restaurant };

type RestaurantCardProps = {
  restaurant: Restaurant;
  onClick?: (restaurant: Restaurant) => void;
};

const formatTime = (time?: string) => {
  if (!time) {
    return '';
  }
  // Convert "09:30" format to "9:30 AM" format
  const [hours, minutes] = time.split(':');
  if (!hours || !minutes) {
    return time; // Return original if format is unexpected
  }
  const hour = Number.parseInt(hours, 10);
  // const ampm = hour >= 12 ? 'PM' : 'AM';
  const displayHour = hour % 12 || 12;
  return `${displayHour}:${minutes}`;
};

const RestaurantCard = ({ restaurant, onClick }: RestaurantCardProps) => {
  const isMobile = useMatches({
    base: true,
    sm: false,
  });

  const imageWidth = isMobile ? 120 : 192;
  const imageHeight = isMobile ? 90 : 144;

  // Get the first image or use a placeholder
  const restaurantImage = restaurant.images?.[0]?.originUrl || '/images/restaurant-placeholder.jpg';

  // Create full address from address object
  const fullAddress = `${restaurant.address.prefectureName} ${restaurant.address.cityName} ${restaurant.address.wardName}`;

  return (
    <Card
      className={classes.card}
      onClick={() => onClick?.(restaurant)}
      radius="lg"
      shadow="sm"
      withBorder
    >
      <Flex
        align="flex-start"
        direction="column"
        gap={isMobile ? 16 : 24}
      >
        {/* Restaurant Image */}
        <div className={classes.imageContainer}>
          <Image
            alt={restaurant.nameEn || restaurant.name}
            className={classes.image}
            height={imageHeight}
            priority={false}
            sizes={`${imageWidth}px`}
            src={restaurantImage}
            style={{
              objectFit: 'cover',
              borderRadius: '8px',
            }}
            width={imageWidth}
          />

          {/* Restaurant Info */}
          <Stack flex={1} gap={12}>
            {/* Restaurant Name & Location */}
            <Stack gap={4}>
              <Text className={classes.restaurantName} lineClamp={1}>
                {restaurant.nameEn || restaurant.name}
              </Text>
              {restaurant.name && restaurant.nameEn && restaurant.name !== restaurant.nameEn && (
                <Text className={classes.restaurantNameJp} lineClamp={1}>
                  {restaurant.name}
                </Text>
              )}
              <Flex gap={{ base: 0, sm: 16 }} direction={{ base: 'column', sm: 'row' }}>
                <Group gap={4}>
                  <LocationIcon />
                  <Text className={classes.location} lineClamp={1}>
                    {fullAddress}
                  </Text>
                </Group>
                <Group gap={4}>
                  <GenreIcon />
                  <Text className={classes.cuisineBadge} variant="light">
                    {restaurant.genre.name}
                  </Text>
                </Group>
              </Flex>
            </Stack>

            {/* Cuisine & Access */}
            <Flex gap={4} align="flex-start">
              <ThemeIcon size={16}>
                <WalkmanIcon />
              </ThemeIcon>
              <Text className={classes.access} lineClamp={2}>
                {restaurant.accessInformationEn || restaurant.accessInformation}
              </Text>
            </Flex>

            {/* Meal Times PC */}
            <>
              {(restaurant.lunchTime || restaurant.dinnerTime) && (
                <div className={classes.mealSection}>
                  <Group gap={16} justify="space-between">
                    {/* Meal Times */}
                    <Group gap={12}>
                      <Flex className={classes.mealTime} gap={6}>
                        <IconSun />
                        <Text className={classes.mealLabel}>
                          Lunch:
                          {restaurant.lunchTime
                            ? (
                                <>
                                  {formatTime(restaurant.lunchTime?.from)}
                                  {' '}
                                  ~
                                  {' '}
                                  {formatTime(restaurant.lunchTime?.to)}
                                </>
                              )
                            : (
                                '-'
                              )}

                        </Text>
                      </Flex>
                      <div className={classes.divider} />

                      <Group className={classes.mealTime} gap={6}>
                        <IConMoon />
                        <Text className={classes.mealLabel}>
                          Dinner:
                          {restaurant.dinnerTime
                            ? (
                                <>
                                  {formatTime(restaurant.dinnerTime?.from)}
                                  {' '}
                                  ~
                                  {' '}
                                  {formatTime(restaurant.dinnerTime?.to)}
                                </>
                              )
                            : (
                                '-'
                              )}
                        </Text>
                      </Group>
                    </Group>
                  </Group>
                </div>
              )}
              {/* Price Range */}
              <div className={classes.priceSection}>
                <Flex align="center" gap={8}>
                  <Flex gap={2}>
                    <Text className={classes.price}>
                      JPY
                      {' '}
                      {currencyFormat(restaurant.lowestMenuPrice)}
                    </Text>
                    <Text className={classes.priceUsd}>
                      (≈
                      {' '}
                      {currencyFormat(restaurant.lowestMenuPriceUSD)}
                      USD)
                    </Text>
                  </Flex>
                  <Text>
                    ~
                  </Text>
                  <Flex gap={2}>
                    <Text className={classes.price}>
                      JPY
                      {' '}
                      {currencyFormat(restaurant.highestMenuPrice)}
                    </Text>
                    {' '}
                    <Text className={classes.priceUsd}>
                      (≈
                      {' '}
                      {currencyFormat(restaurant.highestMenuPriceUSD)}
                      {' '}
                      USD)
                    </Text>
                  </Flex>
                </Flex>
              </div>
            </>
          </Stack>
        </div>
        {/* /* Meal Times and price range Mobile */ }
        <>
          {(restaurant.lunchTime || restaurant.dinnerTime) && (
            <div className={classes.mealSectionMobile} style={{ width: '100%' }}>
              <Flex w="100%">
                {/* Meal Times */}
                <Flex direction="column" justify="center">
                  <Flex gap={6} align="center" justify="center">
                    <ThemeIcon size={16}>
                      <IconSun />
                    </ThemeIcon>
                    <Text className={classes.mealLabel}>
                      Lunch
                    </Text>
                  </Flex>
                  <Flex align="center" justify="center">
                    <Text className={classes.mealTime}>
                      {restaurant.lunchTime
                        ? (
                            <>
                              {formatTime(restaurant.lunchTime?.from)}
                              {' '}
                              ~
                              {' '}
                              {formatTime(restaurant.lunchTime?.to)}
                            </>
                          )
                        : (
                            '-'
                          )}
                    </Text>
                  </Flex>
                </Flex>

                <div className={classes.divider} />
                <div className={classes.mealSectionMobile}>
                  <Flex direction="column">
                    <Flex gap={6} align="center" justify="center">
                      <ThemeIcon size={16}>
                        <IConMoon />
                      </ThemeIcon>
                      <Text className={classes.mealLabel}>
                        Dinner
                      </Text>
                    </Flex>
                    <Flex align="center" justify="center">
                      <Text className={classes.mealTime}>
                        {restaurant.dinnerTime
                          ? (
                              <>
                                {formatTime(restaurant.dinnerTime?.from)}
                                {' '}
                                ~
                                {' '}
                                {formatTime(restaurant.dinnerTime?.to)}
                              </>
                            )
                          : (
                              '-'
                            )}
                      </Text>
                    </Flex>
                  </Flex>
                </div>
              </Flex>
            </div>
          )}
          {/* Price Range */}
          <div className={classes.priceSectionMobile}>
            <Flex align="center" gap={8}>
              <Flex gap={2}>
                <Text className={classes.price}>
                  JPY
                  {' '}
                  {currencyFormat(restaurant.lowestMenuPrice)}
                </Text>
                <Text className={classes.priceUsd}>
                  (≈
                  {' '}
                  {currencyFormat(restaurant.lowestMenuPriceUSD)}
                  USD)
                </Text>
              </Flex>
              <Text>
                ~
              </Text>
              <Flex gap={2}>
                <Text className={classes.price}>
                  JPY
                  {' '}
                  {currencyFormat(restaurant.highestMenuPrice)}
                </Text>
                {' '}
                <Text className={classes.priceUsd}>
                  (≈
                  {' '}
                  {currencyFormat(restaurant.highestMenuPriceUSD)}
                  {' '}
                  USD)
                </Text>
              </Flex>
            </Flex>
          </div>
        </>

      </Flex>
    </Card>
  );
};

export default RestaurantCard;
