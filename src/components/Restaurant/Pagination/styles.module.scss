.container {
  padding: 24px 0;
  border-top: 1px solid var(--mantine-color-gray-3);
  margin-top: 32px;
  width: 704px;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.itemsInfo {
  color: var(--mantine-color-gray-6);
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4;
}

.control {
  --pagination-color: var(--mantine-color-gray-6);
  --pagination-bg: transparent;
  --pagination-bd: var(--mantine-color-gray-4);
  --pagination-hover: var(--mantine-color-gray-1);
  --pagination-hover-color: var(--mantine-color-gray-8);
  --pagination-hover-bd: var(--mantine-color-gray-5);

  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  height: 40px;
  min-width: 40px;
  transition: all 0.2s ease;

  // Active state
  &[data-active] {
    --pagination-color: var(--mantine-color-white);
    --pagination-bg: var(--mantine-color-primary-6);
    --pagination-bd: var(--mantine-color-primary-6);
    --pagination-hover: var(--mantine-color-primary-7);
    --pagination-hover-bd: var(--mantine-color-primary-7);
  }

  // Disabled state (for edge controls when not available)
  &:disabled {
    --pagination-color: var(--mantine-color-gray-4);
    --pagination-bg: transparent;
    --pagination-bd: var(--mantine-color-gray-3);
    cursor: not-allowed;

    &:hover {
      --pagination-bg: transparent;
      --pagination-bd: var(--mantine-color-gray-3);
    }
  }
}

.dots {
  color: var(--mantine-color-gray-5);
  font-size: 14px;
  font-weight: 500;
}

/* Mobile specific adjustments */
@media (max-width: 768px) {
  .container {
    padding: 20px 0;
    margin-top: 24px;
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .control {
    height: 36px;
    min-width: 36px;
    font-size: 13px;
  }

  .itemsInfo {
    font-size: 13px;
  }
}
