'use client';

import { Flex, Pagination as MantinePagination, Text, useMatches } from '@mantine/core';

import classes from './styles.module.scss';

type PaginationProps = {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
};

const Pagination = ({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
}: PaginationProps) => {
  const isMobile = useMatches({
    base: true,
    sm: false,
  });

  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  return (
    <div className={classes.container}>
      {isMobile
        ? (
          // Mobile: Vertical Stack
            <Flex align="center" direction="column" gap={16}>
              <Text className={classes.itemsInfo}>
                page
                {' '}
                {currentPage}
                {' '}
                of
                {' '}
                {totalPages}
                {' '}
                pages (
                {totalItems}
                {' '}
                total items)
              </Text>

              <MantinePagination
                classNames={{
                  control: classes.control,
                  dots: classes.dots,
                }}
                size="md"
                total={totalPages}
                value={currentPage}
                onChange={onPageChange}
                withEdges
              />
            </Flex>
          )
        : (
          // Desktop: Horizontal Layout
            <Flex align="center" justify="space-between">
              <Text className={classes.itemsInfo}>
                {startItem}
                -
                {endItem}
                {' '}
                of
                {' '}
                {totalItems}
                {' '}
                items
              </Text>

              <MantinePagination
                classNames={{
                  control: classes.control,
                  dots: classes.dots,
                }}
                size="md"
                total={totalPages}
                value={currentPage}
                onChange={onPageChange}
                withEdges
              />
            </Flex>
          )}
    </div>
  );
};

export default Pagination;
