'use client';

import { Group, Text } from '@mantine/core';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import classes from './Header.module.css';

const navigationItems = [
  { href: '/restaurant', label: 'Restaurant' },
  { href: '/about', label: 'About' },
  { href: '/faq', label: 'FAQ' },
  { href: '/contact', label: 'Contact Us' },
  { href: '/etiquette', label: 'Etiquette' },
];

const Navigation = () => {
  const pathname = usePathname();

  return (
    <Group gap={32} className={classes.navigation}>
      {navigationItems.map(({ href, label }) => {
        const isActive = pathname === href;
        return (
          <Text
            key={href}
            component={Link}
            href={href}
            size="14px"
            fw={isActive ? 900 : 400}
            c="#111D2F"
            className={classes.navItem}
            style={{
              textDecoration: 'none',
              fontFamily: 'Merriweather, serif',
              fontSize: '14px',
              lineHeight: isActive ? '20px' : '24px',
              cursor: 'pointer',
              whiteSpace: 'nowrap',
            }}
          >
            {label}
          </Text>
        );
      })}
    </Group>
  );
};

export default Navigation;
