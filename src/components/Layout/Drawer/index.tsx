'use client';

import IconCard from '@icons/icon-card.svg';
import IconLogout from '@icons/icon-logout.svg';
import IconUser from '@icons/icon-user.svg';
import { Divider, Drawer, Flex, Group, NavLink, Stack } from '@mantine/core';
import LoginModal from 'components/LoginModal';
import Text from 'components/Typography';
import { useLogout, useUser } from 'hooks';
import { useDrawerStore } from 'hooks/zustand';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { useState } from 'react';
import classes from './Drawer.module.scss';

const navigationItems = [
  { href: '/restaurant', label: 'Restaurant' },
  { href: '/about', label: 'About' },
  { href: '/faq', label: 'FAQ' },
  { href: '/contact', label: 'Contact Us' },
  { href: '/etiquette', label: 'Etiquette' },
];

const AppDrawer = () => {
  const [loginModalOpened, setLoginModalOpened] = useState(false);
  const { openedDrawer, setOpenDrawer } = useDrawerStore();
  const { data: user } = useUser();
  const { confirmLogout } = useLogout();
  const pathname = usePathname();

  const getFullname = () => {
    if (!user?.firstName && !user?.lastName) {
      return 'Unknown User';
    }

    return [user.firstName, user.lastName].join(' ');
  };

  const navLinkClassNames = {
    root: classes.navRoot,
    label: classes.label,
    icon: classes.icon,
  };
  const handleToggleLoginModal = (value: boolean) => setLoginModalOpened(value);

  return (
    <>
      <Drawer
        onClose={() => setOpenDrawer(false)}
        opened={openedDrawer}
        withCloseButton
        withinPortal={false}
      >
        <Stack gap={16} justify="flex-start" mah="100%" p={0}>
          {user
            ? (
                <>
                  <Group gap={0} p={0}>
                    {/* <Avatar
                      alt={getFullname()}
                      radius="xl"
                      size={40}
                      src={getImageUrl(user.avatar)}
                    >
                      <DefaultAvatar />
                    </Avatar> */}
                    <Text fw={500} fz={16}>
                      {getFullname()}
                    </Text>
                  </Group>
                  <Divider />

                  {/* Navigation Items */}
                  {navigationItems.map(({ href, label }) => (
                    <NavLink
                      key={href}
                      classNames={navLinkClassNames}
                      component={Link}
                      href={href}
                      label={(
                        <Text
                          fz={14}
                          fw={pathname === href ? 700 : 400}
                          style={{ fontFamily: 'Merriweather, serif' }}
                        >
                          {label}
                        </Text>
                      )}
                      onClick={() => setOpenDrawer(false)}
                    />
                  ))}

                  <Divider />

                  <NavLink
                    classNames={navLinkClassNames}
                    component={Link}
                    href="/my-page/profile"
                    label={(
                      <Flex align="center" pl={3}>
                        <IconUser
                          style={{
                            width: 18,
                            height: 18,
                          }}
                        />
                        <Text fz={14} ml={8}>My Profile</Text>
                      </Flex>
                    )}
                    onClick={() => setOpenDrawer(false)}
                  />
                  <NavLink
                    classNames={navLinkClassNames}
                    component={Link}
                    href="/my-page/cards"
                    label={(
                      <Flex align="center">
                        <IconCard
                          style={{
                            width: 24,
                            height: 24,
                          }}
                        />
                        <Text fz={14} ml={8}>Payment Methods</Text>
                      </Flex>
                    )}
                    onClick={() => setOpenDrawer(false)}
                  />
                  <NavLink
                    className="logout-btn"
                    classNames={navLinkClassNames}
                    label={(
                      <Flex align="center">
                        <IconLogout />
                        <Text fz={14} c="error.3" ml={8}>Logout</Text>
                      </Flex>
                    )}
                    onClick={() => {
                      setOpenDrawer(false);
                      confirmLogout();
                    }}
                  />
                </>
              )
            : (
                <>
                  <Text
                    classNames={navLinkClassNames}
                    onClick={() => {
                      setOpenDrawer(false);
                      setTimeout(() => handleToggleLoginModal(true), 50);
                    }}
                  >
                    Login
                  </Text>
                  <NavLink
                    classNames={navLinkClassNames}
                    component={Link}
                    href="/register"
                    label="Register"
                    onClick={() => setOpenDrawer(false)}
                    unstyled
                  />
                </>
              )}
        </Stack>
      </Drawer>
      {
        loginModalOpened && (
          <LoginModal
            opened
            onClose={() => handleToggleLoginModal(false)}
          />
        )
      }
    </>
  );
};

export default AppDrawer;
