.input {
  margin: 0;
  --input-bd: var(--mantine-color-gray-5);
  --input-color: var(--mantine-color-gray-9);
  --input-bg: var(--mantine-color-gray-0);
  --input-bd-focus: var(--mantine-color-primary-6);
  --input-placeholder-color: var(--mantine-color-gray-4);
  --input-radius: var(--mantine-radius-sm);
  transition: all 100ms linear;

  &::placeholder {
    color: var(--mantine-color-gray-4);
    opacity: 1;
  }

  &[data-error] {
    --input-bd: var(--mantine-color-error-3);
    --input-color: var(--mantine-color-gray-9);
    --input-placeholder-color: var(--mantine-color-gray-4);

    &:focus,
    &:focus-within {
      --input-bd: var(--mantine-color-error-3);
    }
  }

  &:focus:not([data-error]) {
    box-shadow: 0px 0px 0px 3px rgba(255, 178, 52, 0.4);
  }
}
