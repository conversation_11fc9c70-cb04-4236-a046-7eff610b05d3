.day {
  color: var(--mantine-color-black);
  font-size: 18px;
  line-height: 26px;
  padding: 0 10px;
  margin: 0 4px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transition: background-color 0.2s ease;

  // Hover state for non-selected days
  &:hover:not([data-selected]):not(:disabled):not([data-outside]) {
    color: var(--mantine-color-gray-5);
    background-color: var(--mantine-color-gray-1);
  }

  // Focus state for non-selected days
  &:focus:not([data-selected]):not(:disabled):not([data-outside]) {
    color: var(--mantine-color-gray-5);
    background-color: var(--mantine-color-gray-2);
  }

  // Selected day styles
  &[data-selected] {
    color: var(--mantine-color-white);
    background-color: var(--mantine-color-primary-6);
  }
  &:hover,
  &:active,
  &:focus {
    color: var(--mantine-color-white);
    background-color: var(--mantine-color-primary-6);
  }

  // Disabled and outside days
  &:disabled,
  &[data-outside] {
    color: var(--mantine-color-gray-5);
    cursor: not-allowed;

    &:hover {
      background-color: transparent;
    }
  }
}

.calendarHeader {
  max-width: unset;
}

.calendarHeaderControl {
  svg {
    width: 24px !important;
    height: 24px !important;
    color: var(--mantine-color-primary-6);
  }
}

.calendarHeaderLevel {
  justify-content: center;
  margin-left: 8px;
  font-weight: 700;
  font-size: 16px;
}

.weekday {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  color: var(--mantine-color-gray-5);
  height: 40px;
}

.input {
  margin: 0;
  --input-bd: var(--mantine-color-gray-5);
  --input-color: var(--mantine-color-gray-8);
  --input-bg: var(--mantine-color-gray-0);
  --input-bd-focus: var(--mantine-color-primary-6);
  --input-placeholder-color: var(--mantine-color-gray-4);
  --input-section-color: var(--mantine-color-gray-5);
  /* Disabled state */
  --input-disabled-bg: var(--mantine-color-gray-1);
  --input-disabled-color: var(--mantine-color-gray-5);
  transition: all 100ms linear;
  border-radius: 4px;

  &[data-error] {
    --input-bd: var(--mantine-color-error-3);
    --input-color: var(--mantine-color-gray-8);
    --input-placeholder-color: var(--mantine-color-gray-4);
    --input-section-color: var(--mantine-color-gray-5);
    &:focus,
    &:focus-within {
      --input-bd: var(--mantine-color-error-3);
    }
  }
  &:focus:not([data-error]) {
    box-shadow: 0px 0px 0px 3px rgba(255, 178, 52, 0.4);
  }
}

.label {
  color: var(--mantine-color-gray-8);
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 4px;
}
