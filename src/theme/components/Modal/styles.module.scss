.root {
  --modal-x-offset: 16px;
}

.header {
  padding: 20px 16px 6px 24px;
  align-items: center;

  // Support for custom CloseButton components
  svg {
    width: var(--close-button-size, 32px);
    height: var(--close-button-size, 32px);
  }

  @media (max-width: 48em) {
    padding: 16px 16px 2px 16px;
  }
}

.title {
  font-weight: 700;
  font-size: 18px;
  max-width: calc(100% - 40px);
}

.body {
  font-size: 14px;
  padding: 16px 24px 24px;
  [class~='modal-actions'] {
    margin-top: 40px;
    > button {
      min-width: 96px;
    }
  }
  @media (max-width: 768px) {
    padding: 0px 16px 16px;
    [class~='modal-actions'] {
      margin-top: 32px;
    }
    > button {
      flex: 1;
    }
  }
}

.close {
  svg {
    width: 16px;
    height: 16px;
  }
}
