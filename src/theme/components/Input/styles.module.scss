.wrapper {
  --input-height: 48px;
  --input-radius: 8px;
  --input-fz: 14px;
  --input-line-height: 20px;
  --input-padding-inline-start: 16px;
  --input-padding-inline-end: 16px;
  --left-section-start: 16px;
  --right-section-end: 12px;
  &[data-with-left-section='true'] {
    --input-padding-inline-start: 48px;
  }
  &[data-with-right-section='true'] {
    --input-padding-inline-end: 48px;
  }
}

.section {
  --input-section-color: var(--mantine-color-gray-5);
}

.input {
  margin: 0;
  --input-bd: var(--mantine-color-gray-5);
  --input-color: var(--mantine-color-gray-8);
  --input-bg: var(--mantine-color-gray-0);
  --input-bd-focus: var(--mantine-color-primary-6);
  --input-placeholder-color: var(--mantine-color-gray-4);
  --input-section-color: var(--mantine-color-gray-5);
  /* Disabled state */
  --input-disabled-bg: var(--mantine-color-gray-1);
  --input-disabled-color: var(--mantine-color-gray-5);
  transition: all 100ms linear;
  border-radius: 4px;

  &[data-error] {
    --input-bd: var(--mantine-color-error-3);
    --input-color: var(--mantine-color-gray-8);
    --input-placeholder-color: var(--mantine-color-gray-4);
    --input-section-color: var(--mantine-color-gray-5);
    &:focus,
    &:focus-within {
      --input-bd: var(--mantine-color-error-3);
    }
  }
  &:focus:not([data-error]) {
    box-shadow: 0px 0px 0px 3px rgba(255, 178, 52, 0.4);
  }
}
