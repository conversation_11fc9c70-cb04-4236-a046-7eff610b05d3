// NumberPhoneField theme styles

.selectInput {
  position: relative;
  border-right: none;
  border-color: var(--mantine-color-gray-5);
  border-radius: 4px 0 0 4px !important;
  font-size: 14px;
  height: 48px;
  width: 100%;
  max-width: 125px;
  padding-left: 57px; // Add padding to accommodate flag
  padding-right: 26px;

  &:focus,
  &:focus-within {
    background-color: #ffffff;
  }

  &:hover:not(:focus):not(:focus-within) {
    border-color: var(--mantine-color-gray-5);
    background-color: #ffffff;
  }

  &[data-expanded='true'] {
    box-shadow: none;
  }

  &[data-error='true'] {
    border-color: var(--mantine-color-error-3);

    &:focus,
    &:focus-within {
      border-color: var(--mantine-color-error-3);
      box-shadow: none;
    }
  }
}

.selectSection {
  width: auto;
  padding-left: 0;
}

.textInput {
  padding-left: 0;
  border-radius: 0 4px 4px 0 !important;
  border-left: none;
  border-color: var(--mantine-color-gray-5);
  font-size: 14px;
  height: 48px;

  &::placeholder {
    color: #cdced6;
  }

  &[data-error='true'] {
    border-color: var(--mantine-color-error-3);

    &:focus {
      border-color: var(--mantine-color-error-3);
      box-shadow: none;
    }
  }
}

.clearButton {
  color: var(--mantine-color-gray-6) !important;
  &:hover {
    color: var(--mantine-color-gray-6) !important;
    background-color: transparent !important;
  }

  &:active {
    color: var(--mantine-color-gray-6) !important;
    background-color: transparent !important;
  }

  &:focus {
    color: var(--mantine-color-gray-6) !important;
    background-color: transparent !important;
  }
}

.flag {
  border-radius: 2px;
}

.label {
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 8px;
}

.group {
  &:focus-within {
    border-color: #BC923A;
    box-shadow: 0px 0px 0px 3px rgba(255, 178, 52, 0.4);
    border-radius: 4px;
  }
}

.wrapper {
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: space-between;
}

.dropdown {
  --popover-shadow: var(--mantine-shadow-sm);
  --mantine-shadow-sm: 8px 8px 20px 0px rgba(134, 142, 149, 0.18);
  border: unset;
  width: 100%;
}

.option {
  &[data-checked] {
    svg {
      color: var(--mantine-color-primary-8);
      width: 30px;
      height: 20px;
    }
  }
}
