import type {
  DefaultMantineColor,
  MantineColorsTuple,
  MantineThemeColors,
} from '@mantine/core';
import { DEFAULT_THEME } from '@mantine/core';

const extra = {
  primary: [
    '#FAF7EF',
    '#F1E6D0',
    '#E7D6B1',
    '#DDC592',
    '#D4B573',
    '#CAA553',
    '#BC923A',
    '#9C7A30',
    '#7D6126',
    '#7D6126',
  ],
  yellow: [
    '#FFFADB',
    '#FFF4B8',
    '#FDEC8E',
    '#FDE978',
    '#FAE257',
    '#F9DA26',
    '#FCC73A',
    '#F5BD29',
    '#E8B019',
    '#D59C05',
  ],
  gray: [
    '#FFFFFF',
    '#F5F5F5',
    '#EAEAEA',
    '#E3E3E3',
    '#CECECE',
    '#BABABA',
    '#626262',
    '#828282',
    '#484848',
    '#111D2F',
  ],
  blue: [
    '#ebf5ff',
    '#d4e7fa',
    '#a4cdf7',
    '#007AFF',
    '#4d9af5',
    '#3a8bf5',
    '#3084f6',
    '#2571dc',
    '#1b65c5',
    '#0057ad',
  ],
  green: ['#FFFFFF', '#E7FCD9', '#BBED9A', '#48B301'],
  red: [
    '#FFEEEB',
    '#FECFC6',
    '#FFB5A8',
    '#FB9E8B',
    '#FB8B75',
    '#FF8066',
    '#E96449',
    '#DC563B',
    '#CB4A30',
    '#AB3C26',
  ],
  brown: [
    '#FBF8F4',
    '#F9F5EE',
    '#F6EFE5',
    '#F2E8D9',
    '#EDDEC9',
    '#E2C6A2',
    '#C49669',
    '#996B3D',
    '#83562F',
    '#733C17',
  ],
  magenta: [
    '#FFFFFF',
    '#FFEBF2',
    '#fad2e0',
    '#f89fbe',
    '#f86b9b',
    '#f7437d',
    '#f82e6b',
    '#f92561',
    '#de1c51',
    '#c61348',
  ],
  purple: [
    '#FFFFFF',
    '#EEEBFF',
    '#BEB1FF',
    '#571DB2',
  ],
  lime: ['#FFFFFF', '#FEFFE6', '#fff566', '#F9D807'],
  teal: ['#FFFFFF', '#E2F5F5', '#98DADA', '#1B9DA0'],
  info: ['#FFFFFF', '#def6ff', '#8AD8FF', '#0162D1'],
  warning: ['#FFFFFF', '#FFF8E5', '#FFEDAD', '#E49800'],
  success: ['#FFFFFF', '#ddfdcd', '#B5ED9A', '#16831C'],
  error: ['#FFFFFF', '#FEE2E2', '#F87171', '#B91C1C'],
};

type Colors = keyof typeof extra;

declare module '@mantine/core' {
  export type CustomMantineThemeColorsOverride = {
    colors: Record<Colors | DefaultMantineColor, MantineColorsTuple>;
  };
}

const colors = Object.keys(extra).reduce(
  (acc, curr) => {
    return {
      ...acc,
      [curr]: extra[curr as Colors],
    };
  },
  { ...DEFAULT_THEME.colors },
);

export default colors as MantineThemeColors;
