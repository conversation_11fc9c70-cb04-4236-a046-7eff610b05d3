'use client';

import { createTheme, DEFAULT_THEME, mergeMantineTheme } from '@mantine/core';
import { Merriweather, <PERSON><PERSON> } from 'next/font/google';

import colors from './colors';
import components from './components';
import { variantColorResolver } from './resolver';
import classes from './styles.module.scss';

export const roboto = Roboto({
  subsets: ['latin'],
  weight: ['400', '500', '700', '900'],
  display: 'swap',
  fallback: ['Roboto', 'system-ui', 'Arial', 'sans-serif'], // Add more robust Roboto/sans-serif fallbacks
});

export const merriweather = Merriweather({
  subsets: ['latin'],
  weight: ['400', '700', '900'],
  display: 'swap',
  fallback: ['Merriweather', 'serif'], // Fix: use 'serif' as fallback for Merriweather
});

const themeOverride = createTheme({
  /* Put your mantine theme override here */
  fontFamily: roboto.style.fontFamily,
  fontFamilyMonospace:
    'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace',
  headings: {
    // Controls font-family of h1-h6 tags in Title and TypographyStylesProvider components, fallbacks to theme.fontFamily if not defined
    fontFamily: merriweather.style.fontFamily,
    fontWeight: '700',
    sizes: {
      h1: { fontSize: '40px', lineHeight: '56px', fontWeight: '700' },
      h2: { fontSize: '32px', lineHeight: '46px', fontWeight: '700' },
      h3: { fontSize: '32px', lineHeight: '46px', fontWeight: '700' },
      h4: { fontSize: '28px', lineHeight: '40px', fontWeight: '700' },
      h5: { fontSize: '24px', lineHeight: '34px', fontWeight: '700' },
      h6: { fontSize: '18px', lineHeight: '26px', fontWeight: '700' },
    },
  },
  components,
  primaryColor: 'primary',
  primaryShade: 6,
  white: '#FFFFFF',
  black: '#111D2F',
  colors,
  breakpoints: {
    xs: '30em', // 480px
    sm: '48em', // 767px
    md: '64em', // 1024px
    lg: '74em', // 1184px
    xl: '88em', // 1440px
  },
  fontSizes: {
    xs: '12px',
    sm: '14px',
    md: '16px',
    lg: '18px',
    xl: '24px',
  },
  lineHeights: {
    xs: '16px',
    sm: '20px',
    md: '24px',
    lg: '26px',
    xl: '34px',
  },
  radius: {
    xs: '4px',
    sm: '6px',
    md: '8px',
    lg: '16px',
    xl: '32px',
  },
  shadows: {
    xs: '0px 1px 2px 0px rgba(134, 142, 149, 0.1)',
    sm: '0px 2px 8px 0px rgba(134, 142, 149, 0.16)',
    md: '0px 4px 16px 0px rgba(134, 142, 149, 0.18)',
    lg: '0px 8px 32px 0px rgba(134, 142, 149, 0.32)',
  },
  spacing: {
    xs: '10px',
    sm: '12px',
    md: '16px',
    lg: '20px',
    xl: '24px',
  },
  defaultRadius: 'md',
  activeClassName: '',
  focusRing: 'auto',
  focusClassName: classes['focus-auto'],
  autoContrast: false,
  variantColorResolver,
});

const theme = mergeMantineTheme(DEFAULT_THEME, themeOverride);

export default theme;
