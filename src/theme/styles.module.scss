/* Use `&:focus` when you want focus ring to be visible when control is clicked */
.focus {
  &:focus {
    outline: none;
    // box-shadow: 0px 0px 0px 3px var(--mantine-primary-color-1);
  }
  svg {
    &:focus,
    &:focus-visible,
    &:hover,
    &:active {
      outline: none;
    }
  }
}

/* Use `&:focus-visible` when you want focus ring to be visible
   only when user navigates with keyboard, for example by pressing Tab key */
.focus-auto {
  &:focus-visible {
    outline: none;
  }
  svg {
    &:focus,
    &:focus-visible,
    &:hover,
    &:active {
      outline: none;
    }
  }
}
