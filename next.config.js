/* eslint-disable global-require */
/* eslint-disable import/no-extraneous-dependencies */
let withBundleAnalyzer = (conf) => conf; // Doing nothing

if (process.env.NODE_ENV !== 'production' && process.env.ANALYZE === true) {
  withBundleAnalyzer = require('@next/bundle-analyzer')({
    enabled: true,
  });
}

const nextConfig = {
  output: 'standalone',
  eslint: {
    dirs: ['.'],
  },
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  swcMinify: true,
  poweredByHeader: false,
  reactStrictMode: true,
  sassOptions: {
    prependData: `@import "./_mantine.scss";`,
  },
  productionBrowserSourceMaps: false,
  experimental: {
    scrollRestoration: true,
    optimizePackageImports: [
      "@emotion/css",
      "@emotion/react",
      "@emotion/server",
      "@hookform/resolvers",
      '@mantine/core',
      '@mantine/hooks',
      '@mantine/carousel',
      '@mantine/dates',
      '@mantine/modals',
      '@mantine/next',
      '@mantine/notifications',
      '@mantine/nprogress',
      '@types/lodash',
      '@tanstack/react-query',
      "@next/eslint-plugin-next",
      "@svgr/webpack",
      "@total-typescript/ts-reset",
      "@types/node",
      "@types/react",
      "@types/react-dom",
      "tailwindcss",
      "dayjs",
      "lodash",
      "zustand",
      "axios",
      "yup",
      "sass",
      "sharp",
      "react-hook-form",
      "react-image-crop",
      "react-number-format",
      "linkify-react",
      "linkifyjs",
      "embla-carousel-react",
      "cookies-next",
    ],
    // https://nextjs.org/docs/app/building-your-application/optimizing/memory-usage
    webpackBuildWorker: true,
  },
  modularizeImports: {
    lodash: {
      transform: 'lodash/{{member}}',
    },
    'hooks/zustand': {
      transform: 'hooks/zustand/{{member}}',
    },
    hooks: {
      transform: 'hooks/{{member}}',
    },
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
    ],
    minimumCacheTTL: 86400,
  },
  async rewrites() {
    return [
      {
        source: '/health',
        destination: '/api/health',
      },
      {
        source: '/.well-known/apple-app-site-association',
        destination: '/api/well-known/apple-app-site-association',
      },
      {
        source: '/.well-known/assetlinks.json',
        destination: '/api/well-known/assetlinks',
      },
    ];
  },
  env: {
    API_SERVER_BASE_URL: process.env.API_SERVER_BASE_URL,
    COOKIE_SECURE_PASSWORD: process.env.COOKIE_SECURE_PASSWORD,
    FIREBASE_API_KEY: process.env.FIREBASE_API_KEY,
    FIREBASE_AUTH_DOMAIN: process.env.FIREBASE_AUTH_DOMAIN,
    FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
    FIREBASE_STORAGE_BUCKET: process.env.FIREBASE_STORAGE_BUCKET,
    FIREBASE_MESSAGING_SENDER_ID: process.env.FIREBASE_MESSAGING_SENDER_ID,
    FIREBASE_APP_ID: process.env.FIREBASE_APP_ID,
    FIREBASE_MEASUREMENT_ID: process.env.FIREBASE_MEASUREMENT_ID,
    DATA_TEST_ID: process.env.DATA_TEST_ID,
    S3_MEDIA_BASE_URL: process.env.S3_MEDIA_BASE_URL,
    PROJECT_NAME: process.env.PROJECT_NAME,
    AREA_SERVICE_URL: process.env.AREA_SERVICE_URL,
    BANK_SERVICE_URL: process.env.BANK_SERVICE_URL,
    API_VERITRANS_URL: process.env.API_VERITRANS_URL,
    API_VERITRANS_CLIENT_KEY: process.env.API_VERITRANS_CLIENT_KEY
  },
  webpack(config) {
    // Grab the existing rule that handles SVG imports
    const fileLoaderRule = config.module.rules.find((rule) =>
      rule.test?.test?.('.svg'),
    );

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        test: /\.svg$/i,
        resourceQuery: /url/, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        test: /\.svg$/i,
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] }, // exclude if *.svg?url
        use: [
          {
            loader: '@svgr/webpack',
            options: {
              typescript: true,
              ext: 'tsx',
              prettier: false,
              svgo: true,
              svgoConfig: {
                plugins: [
                  {
                    name: 'preset-default',
                    params: {
                      overrides: { removeViewBox: false, cleanupIds: false },
                    },
                  },
                ],
              },
              titleProp: true,
            },
          },
        ],
      },
    );

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i;
    config.resolve.alias.canvas = false;
    return config;
  },
};

module.exports = withBundleAnalyzer(nextConfig);
