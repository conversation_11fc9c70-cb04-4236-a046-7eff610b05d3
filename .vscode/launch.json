{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "chrome",
      "request": "launch",
      "name": "Next: Chrome",
      "url": "http://localhost:3000",
      "webRoot": "${workspaceFolder}"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Next: Node",
      "program": "${workspaceFolder}/node_modules/.bin/next",
      "args": ["dev"],
      "autoAttachChildProcesses": true,
      "skipFiles": ["<node_internals>/**"],
      "console": "integratedTerminal"
    }
  ],
  "compounds": [
    {
      "name": "Next: Full",
      "configurations": ["Next: Node", "Next: Chrome"]
    }
  ]
}
